/*
 * MainWorld场景控制器
 * 负责确保MainWorld场景正确初始化
 */

using Godot;
using ArchipelagoGame.Manager;

namespace ArchipelagoGame.Scenes
{
    /// <summary>
    /// MainWorld场景控制器
    /// 确保场景完全加载后初始化相关系统
    /// </summary>
    public partial class MainWorldController : Node2D
    {
        #region Godot生命周期

        /// <summary>
        /// 场景准备就绪时调用
        /// </summary>
        public override void _Ready()
        {
            GD.Print("MainWorld场景控制器初始化");
            
            // 延迟初始化，确保所有子节点都已加载
            CallDeferred(MethodName.InitializeScene);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化场景
        /// </summary>
        private void InitializeScene()
        {
            // DepthSortManager会通过节点事件自动初始化，这里不需要手动触发
            // 只有在特殊情况下才需要强制重新初始化
            if (DepthSortManager.Instance != null && !DepthSortManager.Instance.IsInitialized)
            {
                DepthSortManager.Instance.InitializeDepthSorting();
            }
        }

        #endregion
    }
}
