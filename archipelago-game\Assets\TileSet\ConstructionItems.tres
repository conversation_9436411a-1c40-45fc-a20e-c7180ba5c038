[gd_resource type="TileSet" load_steps=7 format=3 uid="uid://covb3a57pgtvd"]

[ext_resource type="Texture2D" uid="uid://dcgfmqavkllk1" path="res://Assets/image/2D Isometric Village Asset Pack/2D Isometric Village Asset Pack/Isometric Assets 3.png" id="1_b5skg"]
[ext_resource type="Texture2D" uid="uid://c0fon5yotop5t" path="res://Assets/image/2D Isometric Village Asset Pack/2D Isometric Village Asset Pack/Isometric Assets 2.png" id="2_j6c2d"]
[ext_resource type="Texture2D" uid="uid://bi7ciwqe16b55" path="res://Assets/image/2D Isometric Village Asset Pack/2D Isometric Village Asset Pack/Isometric Assets 4.png" id="3_8u7hj"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_7x4ua"]
texture = ExtResource("1_b5skg")
texture_region_size = Vector2i(256, 256)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
3:0/0 = 0
4:0/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
4:1/0 = 0
3:1/0 = 0
2:1/0 = 0
2:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, -128, 38.5846, 49.3026, 128)
0:1/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
1:1/0 = 0
1:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, 128, 70.7385, 128, 128)
0:2/0 = 0
0:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-30.0102, 128, -27.8667, 45.0154, 128, -94.3179, 128, 128)
1:2/0 = 0
1:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, -128, -128, 128, -128, 128, -70.7384, 128, 128)
2:2/0 = 0
2:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, -128, -128, 36.441, -128, 128, -70.7384, 128, 128)
3:2/0 = 0
3:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, -128, -72.882, 40.7282, 34.2974, 64.3077, 109.323)
4:2/0 = 0
9:0/0 = 0
7:2/0 = 0
6:2/0 = 0
5:2/0 = 0
0:3/0 = 0
9:2/0 = 0
8:2/0 = 0
4:3/0 = 0
3:3/0 = 0
3:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, -34.2974, -128, -128, 15.0051, -128)
2:3/0 = 0
2:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 94.3179, -128, -128, 128, -128, 128, -38.5846)
1:3/0 = 0
1:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, -45.0154, -128, -128, 128, -128, 128, 83.6)
0:4/0 = 0
9:3/0 = 0
9:4/0 = 0
8:4/0 = 0
8:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 92.1743, -128, -128, -53.5897, -128, 64.3077, -72.882, 81.4564, 8.57437)
7:4/0 = 0
7:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 6.43079, -128, -128, 128, -128, 128, 15.0051, 128, 128)
6:4/0 = 0
6:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 85.7436, -128, -128, 128, -128, 128, 15.0051, -57.8769, 128)
5:4/0 = 0
5:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64.3077, -21.4359, -62.1641, -128, 128, -128, 128, 77.1692)
4:5/0 = 0
3:5/0 = 0
2:5/0 = 0
1:5/0 = 0
0:5/0 = 0
5:3/0 = 0
5:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-49.3026, 128, 128, 4.28717, 128, 128, 128, 128)
6:3/0 = 0
6:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, -128, -79.3128, 128, 6.43079, 128, 128)
7:3/0 = 0
7:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, -128, -2.14359, 128, 96.4615, 128, 128)
8:3/0 = 0
8:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 128, -128, 55.7333, -47.159, 128)
4:4/0 = 0
3:4/0 = 0
2:4/0 = 0
1:4/0 = 0
0:6/0 = 0
0:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(128, -4.28717, 128, 128, -40.7282, 128, -17.1487, 66.4513)
9:5/0 = 0
8:5/0 = 0
7:6/0 = 0
6:6/0 = 0
6:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-96.4615, 30.0103, 6.43077, -30.0102, 68.5948, 62.1641, -12.8615, 94.3179)
5:6/0 = 0
4:6/0 = 0
3:7/0 = 0
2:7/0 = 0
2:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 51.4461, -128, -128, 115.754, -128, 128, -87.8871)
1:7/0 = 0
1:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4.28717, 111.467, -128, 21.4359, -128, -128, 115.754, -128, 128, 55.7333)
0:8/0 = 0
0:7/0 = 0
0:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-36.441, -72.882, -38.5846, -128, 115.754, -128, 145.764, 30.0103)
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, -128, -32.1538, -128, 83.6, -62.1641, 105.036, -10.7179, 10.718, 38.5846, 17.1487, 87.8872, -77.1692, 128, -128, 128)
7:5/0 = 0
5:5/0 = 0
2:6/0 = 0
2:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, -128, -32.1538, -128, 128, -128, 128, 2.14359, 128, 128, -128, 128)
4:7/0 = 0
9:6/0 = 0
8:6/0 = 0
7:7/0 = 0
6:7/0 = 0
5:8/0 = 0
4:8/0 = 0
3:8/0 = 0
2:9/0 = 0
1:9/0 = 0
0:9/0 = 0
1:8/0 = 0
2:8/0 = 0
5:7/0 = 0
9:7/0 = 0
8:7/0 = 0
7:8/0 = 0
6:8/0 = 0
4:9/0 = 0
3:9/0 = 0
8:8/0 = 0
9:8/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
8:9/0 = 0
9:9/0 = 0
1:6/0 = 0
1:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(128, -128, 128, 2.14359, 128, 128, -128, 128, -128, 25.7231)
6:5/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_knbgu"]
texture = ExtResource("2_j6c2d")
texture_region_size = Vector2i(256, 256)
7:0/0 = 0
7:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
8:0/0 = 0
8:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
5:1/0 = 0
5:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
6:1/0 = 0
6:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
7:1/0 = 0
7:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
8:1/0 = 0
8:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
9:1/0 = 0
9:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
4:2/0 = 0
4:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
5:2/0 = 0
5:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
6:2/0 = 0
6:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
7:2/0 = 0
7:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
8:2/0 = 0
8:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
9:2/0 = 0
9:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
3:3/0 = 0
4:3/0 = 0
4:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(12.8615, -32.1538, 2.1436, 77.1692, 36.441, 72.882)
5:3/0 = 0
5:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(12.8615, -32.1538, 2.1436, 77.1692, 36.441, 72.882)
6:3/0 = 0
6:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(12.8615, -32.1538, 2.1436, 77.1692, 36.441, 72.882)
7:3/0 = 0
7:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(15.0051, 72.882, -27.8667, 64.3077, 8.57436, 21.4359, 51.4461, 72.882)
8:3/0 = 0
8:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(15.0051, 72.882, -27.8667, 64.3077, 8.57436, 21.4359, 51.4461, 72.882)
9:3/0 = 0
9:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(15.0051, 72.882, -27.8667, 64.3077, 8.57436, 21.4359, 51.4461, 72.882)
2:4/0 = 0
2:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(128, -128, -36.441, 15.0051, 128, 100.749)
3:4/0 = 0
3:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, -105.036, -128, 105.036, 32.1538, 38.5846)
4:4/0 = 0
4:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(12.8615, -32.1538, 2.1436, 77.1692, 36.441, 72.882)
5:4/0 = 0
5:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(12.8615, -32.1538, 2.1436, 77.1692, 36.441, 72.882)
6:4/0 = 0
6:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(12.8615, -32.1538, 2.1436, 77.1692, 36.441, 72.882)
7:4/0 = 0
7:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(2.1436, 94.3179, -66.4513, 62.1641, 8.57436, 21.4359, 92.1743, 51.4461)
8:4/0 = 0
8:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(60.0205, 83.6, -6.43076, 90.0308, -66.4513, 62.1641, 12.8615, 32.1538, 77.1692, 38.5846)
9:4/0 = 0
9:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(8.57436, 77.1692, -6.43076, 57.8769, 8.57436, 21.4359, 42.8718, 64.3077)
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
2:6/0 = 0
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(4.28719, 94.3179, -62.1641, 51.4461, -109.323, -4.28717, -27.8667, -64.3077, 111.467, 23.5795)
4:6/0 = 0
4:6/0/z_index = 2
5:6/0 = 0
5:6/0/z_index = 2
6:6/0 = 0
6:6/0/z_index = 2
7:6/0 = 0
7:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(55.7333, -128, 15.0051, -55.7333, 128, 17.1487, 128, -122.185)
8:6/0 = 0
8:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-128, 51.4461, 19.2923, -40.7282, -128, -102.892)
9:6/0 = 0
9:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(2.1436, 79.3128, -38.5846, 51.4461, 8.57436, 21.4359, 64.3077, 45.0154)
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
3:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(4.28719, 94.3179, -62.1641, 51.4461, 8.57436, 21.4359, 90.0308, 47.159)
4:7/0 = 0
4:7/0/z_index = 2
5:7/0 = 0
5:7/0/z_index = 2
6:7/0 = 0
6:7/0/z_index = 2
7:7/0 = 0
7:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-6.43076, 49.3026, -83.6, 19.2923, 10.718, -47.159, 85.7436, -8.57436)
8:7/0 = 0
8:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
9:7/0 = 0
9:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
3:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(4.28719, 94.3179, -62.1641, 51.4461, 8.57436, 21.4359, 90.0308, 47.159)
4:8/0 = 0
4:8/0/z_index = 2
5:8/0 = 0
5:8/0/z_index = 2
6:8/0 = 0
6:8/0/z_index = 2
7:8/0 = 0
7:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
8:8/0 = 0
8:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
9:8/0 = 0
9:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
1:9/0 = 0
1:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(4.28719, 94.3179, -62.1641, 51.4461, 8.57436, 21.4359, 90.0308, 47.159)
2:9/0 = 0
3:9/0 = 0
3:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(4.28719, 94.3179, -62.1641, 51.4461, 8.57436, 21.4359, 90.0308, 47.159)
4:9/0 = 0
5:9/0 = 0
5:9/0/z_index = 2
5:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(25.7231, -12.8615, -45.0154, 49.3026, 30.0103, 83.6, 90.0308, 42.8718)
6:9/0 = 0
7:9/0 = 0
7:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
8:9/0 = 0
8:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
9:9/0 = 0
9:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.57436, 94.3179, -62.1641, 51.4461, -2.14359, 12.8615, 66.4513, 57.8769)
2:1/0 = 0
2:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
1:1/0 = 0
1:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
0:2/0 = 0
0:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
1:2/0 = 0
1:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
3:1/0 = 0
3:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
4:1/0 = 0
4:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
5:0/0 = 0
5:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
4:0/0 = 0
4:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
6:0/0 = 0
6:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
0:1/0 = 0
0:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
9:0/0 = 0
9:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
1:0/0 = 0
1:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
3:2/0 = 0
3:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
2:3/0 = 0
1:3/0 = 0
0:3/0 = 0
2:2/0 = 0
2:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(107.179, 45.0154, -15.0051, 102.892, -98.6051, 51.4461, -8.57436, -6.43076)
0:4/0 = 0
0:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(128, -128, -98.6051, -36.441, 30.0103, 83.6, 128, 77.1692)
1:5/0 = 0
1:6/0 = 0
0:8/0 = 0
0:7/0 = 0
0:6/0 = 0
0:5/0 = 0
1:4/0 = 0
1:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(83.6, -66.4513, -128, -105.036, -128, 72.882, 64.3077, 0)
0:9/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_op8jx"]
texture = ExtResource("3_8u7hj")
texture_region_size = Vector2i(256, 256)

[resource]
tile_size = Vector2i(256, 256)
physics_layer_0/collision_layer = 8
sources/0 = SubResource("TileSetAtlasSource_7x4ua")
sources/1 = SubResource("TileSetAtlasSource_knbgu")
sources/2 = SubResource("TileSetAtlasSource_op8jx")
