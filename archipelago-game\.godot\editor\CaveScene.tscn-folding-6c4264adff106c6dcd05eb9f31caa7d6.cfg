[folding]

node_unfolds=[Node<PERSON>ath("Environment/TileMapLayer"), PackedStringArray("Rendering", "tile_set"), NodePath("CaveExit/CaveExit1/CaveBarrier/BarrierCollision"), PackedStringArray("shape", "Transform")]
resource_unfolds=["res://src/Scenes/GameViwes/CaveScene.tscn::TileSet_mkl61", PackedStringArray("terrain_set__array", "terrain_set_0/terrain__array", "physics_layer__array"), "res://src/Scenes/GameViwes/CaveScene.tscn::CircleShape2D_ed0bx", PackedStringArray(), "res://src/Scenes/GameViwes/CaveScene.tscn::CircleShape2D_mkl61", PackedStringArray()]
nodes_folded=[NodePath("CaveExit/CaveExit1/CaveBarrier")]
