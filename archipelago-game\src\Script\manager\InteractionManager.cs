/*
 * 交互管理器 - 负责场景切换和交互状态管理
 */

using Godot;
using System;
using System.Collections.Generic;
using ArchipelagoGame.UI;

namespace ArchipelagoGame.Interaction
{
    /// <summary>
    /// 交互管理器 - 统一管理场景切换和交互逻辑
    /// 单例模式，全局唯一实例
    /// </summary>
    public partial class InteractionManager : Node
    {
        #region 单例模式
        
        private static InteractionManager _instance;
        public static InteractionManager Instance => _instance;
        
        #endregion
        
        #region 信号定义
        
        /// <summary>场景切换开始信号</summary>
        [Signal]
        public delegate void SceneTransitionStartedEventHandler(string fromScene, string toScene);
        
        /// <summary>场景切换完成信号</summary>
        [Signal]
        public delegate void SceneTransitionCompletedEventHandler(string sceneName);
        
        /// <summary>玩家位置设置信号</summary>
        [Signal]
        public delegate void PlayerPositionSetEventHandler(Vector2 position);
        
        #endregion
        
        #region 私有字段

        /// <summary>场景状态数据</summary>
        private Dictionary<string, SceneStateData> _sceneStates = new();

        /// <summary>洞穴记忆数据</summary>
        private Dictionary<string, CaveMemoryData> _caveMemories = new();

        /// <summary>当前场景路径</summary>
        private string _currentScenePath = "";

        /// <summary>玩家生成位置</summary>
        private Vector2 _playerSpawnPosition = Vector2.Zero;

        /// <summary>是否需要设置玩家位置</summary>
        private bool _shouldSetPlayerPosition = false;

        #endregion
        
        #region Godot生命周期
        
        public override void _Ready()
        {
            _instance = this;
            _currentScenePath = GetTree().CurrentScene?.SceneFilePath ?? "";

            // 连接场景切换信号
            GetTree().NodeAdded += OnNodeAdded;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 切换到洞穴场景 - 支持智能位置记忆
        /// </summary>
        /// <param name="caveScenePath">洞穴场景路径</param>
        /// <param name="baseSpawnPosition">基础生成位置</param>
        /// <param name="spawnRadius">生成半径</param>
        public void TransitionToCave(string caveScenePath, Vector2 baseSpawnPosition, float spawnRadius = 50f)
        {
            GD.Print($"切换到洞穴场景: {caveScenePath}");

            // 保存当前主世界状态
            SaveCurrentSceneState();

            // 获取或创建洞穴记忆数据
            if (!_caveMemories.TryGetValue(caveScenePath, out var caveMemory))
            {
                caveMemory = new CaveMemoryData
                {
                    CaveScenePath = caveScenePath,
                    HasVisited = false
                };
                _caveMemories[caveScenePath] = caveMemory;
            }

            // 保存进入洞穴前的主世界位置
            var player = GetTree().GetFirstNodeInGroup("player") as Node2D;
            if (player != null)
            {
                caveMemory.MainWorldPosition = player.GlobalPosition;
            }

            // 根据访问状态确定生成位置
            Vector2 finalSpawnPosition;
            if (!caveMemory.HasVisited)
            {
                // 首次访问：在基础位置附近随机生成
                finalSpawnPosition = GetRandomPositionInRadius(baseSpawnPosition, spawnRadius);
                caveMemory.HasVisited = true;
                GD.Print($"首次进入洞穴，随机生成位置: {finalSpawnPosition}");
            }
            else
            {
                // 再次访问：使用记忆位置
                finalSpawnPosition = caveMemory.LastCavePosition;
                GD.Print($"返回洞穴，使用记忆位置: {finalSpawnPosition}");
            }

            // 更新访问时间
            caveMemory.LastVisitTime = Time.GetUnixTimeFromSystem();

            // 设置玩家生成位置
            _playerSpawnPosition = finalSpawnPosition;
            _shouldSetPlayerPosition = true;

            // 发出场景切换开始信号
            EmitSignal(SignalName.SceneTransitionStarted, _currentScenePath, caveScenePath);

            // 使用UIManager进行场景切换
            UIManager.Instance?.SwitchToScene(caveScenePath);

            // 延迟更新当前场景路径
            CallDeferred(MethodName.UpdateCurrentScenePath, caveScenePath);
        }
        
        /// <summary>
        /// 返回主世界 - 支持洞穴位置记忆
        /// </summary>
        public void ReturnToMainWorld()
        {
            string mainWorldPath = "res://src/Scenes/GameViwes/MainWorld.tscn";
            GD.Print($"返回主世界: {mainWorldPath}");

            // 保存当前洞穴内的玩家位置
            SaveCurrentCavePosition();

            // 获取返回主世界的位置
            Vector2 returnPosition = GetMainWorldReturnPosition();

            _playerSpawnPosition = returnPosition;
            _shouldSetPlayerPosition = true;

            GD.Print($"返回主世界位置: {returnPosition}");

            EmitSignal(SignalName.SceneTransitionStarted, _currentScenePath, mainWorldPath);
            UIManager.Instance?.SwitchToScene(mainWorldPath);

            // 延迟更新当前场景路径
            CallDeferred(MethodName.UpdateCurrentScenePath, mainWorldPath);
        }
        
        /// <summary>
        /// 获取玩家生成位置
        /// </summary>
        public Vector2 GetPlayerSpawnPosition()
        {
            return _playerSpawnPosition;
        }
        
        /// <summary>
        /// 检查是否需要设置玩家位置
        /// </summary>
        public bool ShouldSetPlayerPosition()
        {
            return _shouldSetPlayerPosition;
        }
        
        /// <summary>
        /// 标记玩家位置已设置
        /// </summary>
        public void MarkPlayerPositionSet()
        {
            _shouldSetPlayerPosition = false;
            EmitSignal(SignalName.PlayerPositionSet, _playerSpawnPosition);
        }
        
        /// <summary>
        /// 获取场景状态数据
        /// </summary>
        public SceneStateData GetSceneState(string scenePath)
        {
            return _sceneStates.TryGetValue(scenePath, out var state) ? state : null;
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 保存当前场景状态
        /// </summary>
        private void SaveCurrentSceneState()
        {
            var player = GetTree().GetFirstNodeInGroup("player") as Node2D;
            if (player != null)
            {
                var stateData = new SceneStateData
                {
                    ScenePath = _currentScenePath,
                    PlayerPosition = player.GlobalPosition,
                    Timestamp = Time.GetUnixTimeFromSystem()
                };
                
                _sceneStates[_currentScenePath] = stateData;
                GD.Print($"保存场景状态: {_currentScenePath}, 玩家位置: {player.GlobalPosition}");
            }
        }
        
        /// <summary>
        /// 当新节点添加到场景树时调用
        /// </summary>
        private void OnNodeAdded(Node node)
        {
            // 检测玩家节点添加，用于设置位置
            if (node.IsInGroup("player") && _shouldSetPlayerPosition)
            {
                CallDeferred(MethodName.SetPlayerPositionDeferred, node);
            }
        }
        
        /// <summary>
        /// 延迟设置玩家位置
        /// </summary>
        private void SetPlayerPositionDeferred(Node playerNode)
        {
            if (playerNode is Node2D player && _shouldSetPlayerPosition)
            {
                player.GlobalPosition = _playerSpawnPosition;
                MarkPlayerPositionSet();
                GD.Print($"设置玩家位置: {_playerSpawnPosition}");
            }
        }

        /// <summary>
        /// 更新当前场景路径
        /// </summary>
        private void UpdateCurrentScenePath(string newScenePath)
        {
            _currentScenePath = newScenePath;
            GD.Print($"场景已切换到: {_currentScenePath}");

            // 发出场景切换完成信号
            EmitSignal(SignalName.SceneTransitionCompleted, _currentScenePath);
        }

        /// <summary>
        /// 在指定半径内生成随机位置
        /// </summary>
        private Vector2 GetRandomPositionInRadius(Vector2 center, float radius)
        {
            var random = new Random();
            float angle = (float)(random.NextDouble() * 2 * Math.PI);
            float distance = (float)(random.NextDouble() * radius);

            float x = center.X + distance * Mathf.Cos(angle);
            float y = center.Y + distance * Mathf.Sin(angle);

            return new Vector2(x, y);
        }

        /// <summary>
        /// 保存当前洞穴内的玩家位置
        /// </summary>
        private void SaveCurrentCavePosition()
        {
            var player = GetTree().GetFirstNodeInGroup("player") as Node2D;
            if (player != null && _caveMemories.TryGetValue(_currentScenePath, out var caveMemory))
            {
                caveMemory.LastCavePosition = player.GlobalPosition;
                GD.Print($"保存洞穴位置: {_currentScenePath} -> {player.GlobalPosition}");
            }
        }

        /// <summary>
        /// 获取返回主世界的位置
        /// </summary>
        private Vector2 GetMainWorldReturnPosition()
        {
            // 尝试从洞穴记忆中获取进入前的主世界位置
            if (_caveMemories.TryGetValue(_currentScenePath, out var caveMemory))
            {
                if (caveMemory.MainWorldPosition != Vector2.Zero)
                {
                    return caveMemory.MainWorldPosition;
                }
            }

            // 如果没有记忆位置，使用默认位置
            GD.Print("警告：未找到主世界返回位置，使用默认位置");
            return new Vector2(400, 400);
        }

        #endregion
    }
    
    /// <summary>
    /// 场景状态数据类
    /// </summary>
    public class SceneStateData
    {
        public string ScenePath { get; set; } = "";
        public Vector2 PlayerPosition { get; set; } = Vector2.Zero;
        public double Timestamp { get; set; } = 0.0;
    }

    /// <summary>
    /// 洞穴记忆数据类 - 用于记录洞穴访问状态和位置信息
    /// </summary>
    public class CaveMemoryData
    {
        /// <summary>洞穴场景路径</summary>
        public string CaveScenePath { get; set; } = "";

        /// <summary>洞穴内最后位置</summary>
        public Vector2 LastCavePosition { get; set; } = Vector2.Zero;

        /// <summary>进入洞穴前的主世界位置</summary>
        public Vector2 MainWorldPosition { get; set; } = Vector2.Zero;

        /// <summary>是否已访问过</summary>
        public bool HasVisited { get; set; } = false;

        /// <summary>最后访问时间</summary>
        public double LastVisitTime { get; set; } = 0.0;
    }
}
