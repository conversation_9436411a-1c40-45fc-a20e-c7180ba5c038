/*
 * 深度排序管理器 - 2D等距视角深度渲染解决方案
 * 负责管理基于Y坐标的深度排序，确保角色和建筑物正确的前后关系
 */

using Godot;

namespace ArchipelagoGame.Manager
{
    /// <summary>
    /// 深度排序管理器 - 处理2D等距视角的深度渲染
    /// 使用Godot 4.x的Y-Sort功能实现基于Y坐标的自动深度排序
    /// </summary>
    public partial class DepthSortManager : Node
    {
        #region 单例模式

        /// <summary>单例实例</summary>
        public static DepthSortManager Instance { get; private set; }

        #endregion

        #region 公共属性

        /// <summary>是否已经初始化</summary>
        public bool IsInitialized => _isInitialized;

        #endregion

        #region 导出属性

        /// <summary>是否启用调试信息输出</summary>
        [Export]
        public bool EnableDebugOutput { get; set; } = false;

        /// <summary>深度排序的基础偏移值</summary>
        [Export]
        public int BaseDepthOffset { get; set; } = 1000;

        #endregion

        #region 私有字段

        /// <summary>游戏世界根节点引用</summary>
        private Node2D _gameWorldRoot;

        /// <summary>玩家节点引用</summary>
        private CharacterBody2D _player;

        /// <summary>是否已经初始化</summary>
        private bool _isInitialized = false;

        /// <summary>MainWorld场景路径</summary>
        private const string MainWorldScenePath = "res://src/Scenes/GameViwes/MainWorld.tscn";

        #endregion

        #region Godot生命周期

        /// <summary>
        /// 节点准备就绪时初始化
        /// </summary>
        public override void _Ready()
        {
            // 设置单例
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                QueueFree();
                return;
            }

            // 监听场景切换事件
            GetTree().NodeAdded += OnNodeAdded;
            GetTree().NodeRemoved += OnNodeRemoved;

            // 检查当前场景是否为MainWorld
            CallDeferred(MethodName.CheckCurrentScene);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 检查当前场景是否为MainWorld
        /// </summary>
        private void CheckCurrentScene()
        {
            var currentScene = GetTree().CurrentScene;
            var scenePath = currentScene?.SceneFilePath ?? "";

            if (IsMainWorldScene(scenePath))
            {
                InitializeDepthSorting();
            }
        }

        /// <summary>
        /// 检查是否为MainWorld场景
        /// </summary>
        /// <param name="scenePath">场景路径</param>
        /// <returns>是否为MainWorld场景</returns>
        private static bool IsMainWorldScene(string scenePath)
        {
            return scenePath == MainWorldScenePath;
        }

        /// <summary>
        /// 节点添加事件处理
        /// </summary>
        /// <param name="node">添加的节点</param>
        private void OnNodeAdded(Node node)
        {
            // 检查是否是MainWorld场景的根节点
            if (node.Name == "MainWorld" && !_isInitialized)
            {
                CallDeferred(MethodName.InitializeDepthSorting);
            }
        }

        /// <summary>
        /// 节点移除事件处理
        /// </summary>
        /// <param name="node">移除的节点</param>
        private void OnNodeRemoved(Node node)
        {
            // 如果MainWorld节点被移除，重置初始化状态
            if (node.Name == "MainWorld" && _isInitialized)
            {
                ResetDepthSorting();
            }
        }

        /// <summary>
        /// 初始化深度排序系统
        /// </summary>
        public void InitializeDepthSorting()
        {
            // 如果已经初始化，跳过
            if (_isInitialized)
            {
                return;
            }

            var currentScene = GetTree().CurrentScene;

            // 检查是否为MainWorld场景
            if (!IsMainWorldScene(currentScene?.SceneFilePath ?? ""))
            {
                return;
            }

            GD.Print("DepthSortManager: 开始初始化");

            // 尝试查找节点
            if (TryFindNodes())
            {
                CompleteInitialization();
            }
            else
            {
                // 延迟重试一次
                GetTree().CreateTimer(0.5).Timeout += () => {
                    if (TryFindNodes())
                    {
                        CompleteInitialization();
                    }
                    else
                    {
                        GD.PrintErr("DepthSortManager: 初始化失败，无法找到必要节点");
                    }
                };
            }
        }

        /// <summary>
        /// 尝试查找所需的节点
        /// </summary>
        /// <returns>是否成功找到所有节点</returns>
        private bool TryFindNodes()
        {
            // 使用直接路径查找，更高效
            _gameWorldRoot = GetNodeOrNull<Node2D>("/root/MainWorld/VisualSorting/GameWorld");
            if (_gameWorldRoot == null) return false;

            var playerInstance = GetNodeOrNull<Node>("/root/MainWorld/VisualSorting/player");
            if (playerInstance == null) return false;

            _player = playerInstance.GetNodeOrNull<CharacterBody2D>("Player");
            return _player != null;
        }

        /// <summary>
        /// 完成初始化配置
        /// </summary>
        private void CompleteInitialization()
        {
            ConfigureYSortForGameWorld();
            ConfigurePlayerDepthSorting();
            _isInitialized = true;

            GD.Print("DepthSortManager: 初始化完成");
        }

        /// <summary>
        /// 重置深度排序状态
        /// </summary>
        private void ResetDepthSorting()
        {
            _isInitialized = false;
            _gameWorldRoot = null;
            _player = null;
        }

        /// <summary>
        /// 为游戏世界配置Y-Sort
        /// </summary>
        public void ConfigureYSortForGameWorld()
        {
            if (_gameWorldRoot == null) return;

            _gameWorldRoot.YSortEnabled = true;
            ConfigureTileMapLayers();
        }

        /// <summary>
        /// 配置TileMapLayer的深度排序
        /// </summary>
        private void ConfigureTileMapLayers()
        {
            // 配置Building层
            var buildingLayer = _gameWorldRoot.GetNodeOrNull<TileMapLayer>("Building");
            if (buildingLayer != null)
            {
                buildingLayer.ZIndex = 0;
                buildingLayer.YSortEnabled = true;
            }

            // 配置Plant层
            var plantLayer = _gameWorldRoot.GetNodeOrNull<TileMapLayer>("Plant");
            if (plantLayer != null)
            {
                plantLayer.ZIndex = 0;
                plantLayer.YSortEnabled = true;
            }

            // GroundLayer保持在底层
            var groundLayer = _gameWorldRoot.GetNodeOrNull<TileMapLayer>("GroundLayer");
            if (groundLayer != null)
            {
                groundLayer.ZIndex = -1;
                groundLayer.YSortEnabled = true;
            }
        }

        /// <summary>
        /// 配置玩家的深度排序
        /// </summary>
        public void ConfigurePlayerDepthSorting()
        {
            if (_player == null) return;

            _player.YSortEnabled = true;

            var playerSprite = _player.GetNodeOrNull<Sprite2D>("PlayerSprite");
            if (playerSprite != null)
            {
                playerSprite.ZIndex = 0;
            }
        }



        /// <summary>
        /// 获取节点的渲染深度（用于调试）
        /// </summary>
        /// <param name="node">要检查的节点</param>
        /// <returns>渲染深度信息</returns>
        public static string GetNodeDepthInfo(Node2D node)
        {
            if (node == null) return "节点为空";

            return $"节点: {node.Name}, 位置: {node.GlobalPosition}, Z-Index: {node.ZIndex}, Y-Sort: {node.YSortEnabled}";
        }

        /// <summary>
        /// 打印所有相关节点的深度信息（调试用）
        /// </summary>
        public void PrintDepthDebugInfo()
        {
            if (!EnableDebugOutput) return;

            GD.Print("=== 深度排序调试信息 ===");

            if (_gameWorldRoot != null)
            {
                GD.Print(GetNodeDepthInfo(_gameWorldRoot));

                // 打印各个层的信息
                var layers = new string[] { "GroundLayer", "Plant", "Building" };
                foreach (var layerName in layers)
                {
                    var layer = _gameWorldRoot.GetNodeOrNull<TileMapLayer>(layerName);
                    if (layer != null)
                    {
                        GD.Print($"  {layerName}: Z-Index={layer.ZIndex}, Y-Sort={layer.YSortEnabled}");
                    }
                }
            }

            if (_player != null)
            {
                GD.Print(GetNodeDepthInfo(_player));

                var playerSprite = _player.GetNodeOrNull<Sprite2D>("PlayerSprite");
                if (playerSprite != null)
                {
                    GD.Print($"  PlayerSprite: Z-Index={playerSprite.ZIndex}");
                }
            }

            GD.Print("=== 调试信息结束 ===");
        }



        /// <summary>
        /// 手动重新初始化深度排序系统
        /// 用于场景切换后重新初始化
        /// </summary>
        public void ReinitializeDepthSorting()
        {
            ResetDepthSorting();
            InitializeDepthSorting();
        }

        /// <summary>
        /// 强制初始化深度排序系统（忽略场景检查）
        /// 用于特殊情况下的强制初始化
        /// </summary>
        public void ForceInitializeDepthSorting()
        {
            ResetDepthSorting();

            if (TryFindNodes())
            {
                CompleteInitialization();
            }
            else
            {
                GD.PrintErr("DepthSortManager: 强制初始化失败");
            }
        }

        #endregion

        #region 清理

        /// <summary>
        /// 节点退出场景树时清理
        /// </summary>
        public override void _ExitTree()
        {
            // 清理事件监听
            if (GetTree() != null)
            {
                GetTree().NodeAdded -= OnNodeAdded;
                GetTree().NodeRemoved -= OnNodeRemoved;
            }

            if (Instance == this)
            {
                Instance = null;
            }
        }

        #endregion
    }
}
