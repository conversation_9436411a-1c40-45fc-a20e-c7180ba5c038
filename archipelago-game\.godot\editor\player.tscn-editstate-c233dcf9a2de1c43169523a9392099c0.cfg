[editor_states]

Anim={
"visible": false
}
2D={
"grid_offset": Vector2(0, 0),
"grid_snap_active": false,
"grid_step": Vector2(8, 8),
"grid_visibility": 1,
"ofs": Vector2(26.1392, 256.993),
"primary_grid_step": Vector2i(8, 8),
"show_group_gizmos": true,
"show_guides": true,
"show_helpers": false,
"show_lock_gizmos": true,
"show_origin": true,
"show_position_gizmos": true,
"show_rulers": true,
"show_transformation_gizmos": true,
"show_viewport": true,
"show_zoom_control": true,
"smart_snap_active": false,
"snap_guides": true,
"snap_node_anchors": true,
"snap_node_center": true,
"snap_node_parent": true,
"snap_node_sides": true,
"snap_other_nodes": true,
"snap_pixel": true,
"snap_relative": false,
"snap_rotation": false,
"snap_rotation_offset": 0.0,
"snap_rotation_step": 0.261799,
"snap_scale": false,
"snap_scale_step": 0.1,
"zoom": 1.77156
}
3D={
"fov": 70.01,
"gizmos_status": {
"AudioListener3D": 0,
"AudioStreamPlayer3D": 0,
"CPUParticles3D": 0,
"CSGShape3D": 0,
"Camera3D": 0,
"CollisionObject3D": 0,
"CollisionPolygon3D": 0,
"CollisionShape3D": 0,
"Decal": 0,
"FogVolume": 0,
"GPUParticles3D": 0,
"GPUParticlesCollision3D": 0,
"Joint3D": 0,
"Light3D": 0,
"LightmapGI": 0,
"LightmapProbe": 0,
"Marker3D": 0,
"MeshInstance3DCustomAABB": 0,
"NavigationLink3D": 0,
"NavigationObstacle3D": 0,
"NavigationRegion3D": 0,
"OccluderInstance3D": 0,
"Particles3DEmissionShape": 0,
"Path3D": 0,
"PhysicalBone3D": 0,
"RayCast3D": 0,
"ReflectionProbe": 0,
"ShapeCast3D": 0,
"Skeleton3D": 0,
"SoftBody3D": 0,
"SpringArm3D": 0,
"SpringBoneCollision3D": 0,
"SpringBoneSimulator3D": 0,
"VehicleWheel3D": 0,
"VisibleOnScreenNotifier3D": 0,
"VoxelGI": 0
},
"local_coords": false,
"preview_sun_env": {
"environ_ao_enabled": false,
"environ_enabled": true,
"environ_energy": 1.0,
"environ_gi_enabled": false,
"environ_glow_enabled": true,
"environ_ground_color": Color(0.2, 0.169, 0.133, 1),
"environ_sky_color": Color(0.385, 0.454, 0.55, 1),
"environ_tonemap_enabled": true,
"sun_color": Color(1, 1, 1, 1),
"sun_enabled": true,
"sun_energy": 1.0,
"sun_max_distance": 100.0,
"sun_rotation": Vector2(-1.0472, 2.61799)
},
"rotate_snap": 15.0,
"scale_snap": 10.0,
"show_grid": true,
"show_origin": true,
"snap_enabled": false,
"translate_snap": 1.0,
"viewport_mode": 1,
"viewports": [{
"auto_orthogonal": false,
"auto_orthogonal_enabled": true,
"cinematic_preview": false,
"display_mode": 22,
"distance": 4.0,
"doppler": false,
"frame_time": false,
"gizmos": true,
"grid": true,
"half_res": false,
"information": false,
"listener": true,
"lock_rotation": false,
"orthogonal": false,
"position": Vector3(0, 0, 0),
"transform_gizmo": true,
"use_environment": false,
"view_type": 0,
"x_rotation": 0.5,
"y_rotation": -0.5
}, {
"auto_orthogonal": false,
"auto_orthogonal_enabled": true,
"cinematic_preview": false,
"display_mode": 22,
"distance": 4.0,
"doppler": false,
"frame_time": false,
"gizmos": true,
"grid": true,
"half_res": false,
"information": false,
"listener": false,
"lock_rotation": false,
"orthogonal": false,
"position": Vector3(0, 0, 0),
"transform_gizmo": true,
"use_environment": false,
"view_type": 0,
"x_rotation": 0.5,
"y_rotation": -0.5
}, {
"auto_orthogonal": false,
"auto_orthogonal_enabled": true,
"cinematic_preview": false,
"display_mode": 22,
"distance": 4.0,
"doppler": false,
"frame_time": false,
"gizmos": true,
"grid": true,
"half_res": false,
"information": false,
"listener": false,
"lock_rotation": false,
"orthogonal": false,
"position": Vector3(0, 0, 0),
"transform_gizmo": true,
"use_environment": false,
"view_type": 0,
"x_rotation": 0.5,
"y_rotation": -0.5
}, {
"auto_orthogonal": false,
"auto_orthogonal_enabled": true,
"cinematic_preview": false,
"display_mode": 22,
"distance": 4.0,
"doppler": false,
"frame_time": false,
"gizmos": true,
"grid": true,
"half_res": false,
"information": false,
"listener": false,
"lock_rotation": false,
"orthogonal": false,
"position": Vector3(0, 0, 0),
"transform_gizmo": true,
"use_environment": false,
"view_type": 0,
"x_rotation": 0.5,
"y_rotation": -0.5
}],
"zfar": 4000.01,
"znear": 0.05
}
Game={
"camera_override_mode": 2,
"hide_selection": false,
"select_mode": 0
}
selected_nodes=Array[NodePath]([NodePath("/root/@EditorNode@21301/@Panel@14/@VBoxContainer@15/DockHSplitLeftL/DockHSplitLeftR/DockHSplitMain/@VBoxContainer@26/DockVSplitCenter/@VSplitContainer@54/@VBoxContainer@55/@EditorMainScreen@102/MainScreen/@CanvasItemEditor@11482/@VSplitContainer@11134/@HSplitContainer@11136/@HSplitContainer@11138/@Control@11139/@SubViewportContainer@11140/@SubViewport@11141/MainWorld/VisualSorting/GameWorld/Plant")])
